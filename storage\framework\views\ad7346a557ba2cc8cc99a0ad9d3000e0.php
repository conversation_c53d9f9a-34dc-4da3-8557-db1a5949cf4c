<?php

use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Locked;
use Livewire\Volt\Component;

?>

<main class="flex items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-md px-6 py-10 space-y-6">

        <!-- Header -->
        <div class="flex flex-col items-center space-y-3">
            <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="Avatar" class="w-20 h-20 rounded-full">
            <h2 class="text-2xl font-bold text-center">Set New Password</h2>
            <p class="text-center text-gray-500">
                Create a new strong password to secure your account.
            </p>
        </div>

        <!-- Session Status -->
        <!--[if BLOCK]><![endif]--><?php if(session('status')): ?>
            <div class="p-3 text-sm text-green-600 bg-green-100 border border-green-300 rounded">
                <?php echo e(session('status')); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Livewire Form -->
        <form wire:submit="resetPassword" class="space-y-4">

            <!-- Email (hidden field) -->
            <input
                type="hidden"
                wire:model="email"
                name="email"
            />

            <!-- New Password -->
            <input
                type="password"
                wire:model.defer="password"
                placeholder="New Password"
                class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                required
                autocomplete="new-password"
            />
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-sm text-red-600"><?php echo e($message); ?></p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Confirm Password -->
            <input
                type="password"
                wire:model.defer="password_confirmation"
                placeholder="Confirm Password"
                class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                required
                autocomplete="new-password"
            />
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <p class="text-sm text-red-600"><?php echo e($message); ?></p>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Submit Button -->
            <button type="submit" class="w-full px-4 py-3 font-semibold text-white bg-black rounded-md">
                Reset Password
            </button>
        </form>

        <!-- Back to Login -->
        <p class="text-sm text-center text-gray-400">
            Back to <a href="<?php echo e(route('login')); ?>" class="font-semibold hover:underline">Login</a>
        </p>
    </div>
</main><?php /**PATH C:\laragon\www\travel-platform\resources\views\livewire/pages/auth/reset-password.blade.php ENDPATH**/ ?>