<?php

use App\Livewire\Forms\LoginForm;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

?>

<main class="flex items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-md px-6 py-10 space-y-6">
        <!-- Header -->
        <div class="flex flex-col items-center space-y-3">
            <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="Avatar" class="w-20 h-20 rounded-full">
            <h2 class="text-2xl font-bold text-center">Welcome back!</h2>
            <p class="text-center text-gray-500">Stay signed in with your account</p>
        </div>

        <!-- Livewire Form -->
        <form wire:submit="login">
            <?php echo csrf_field(); ?>

            <!-- Email -->
            <div class="mb-4">
                <input wire:model="form.email" 
                       type="email" 
                       placeholder="Email" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                       required autofocus>
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['form.email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> 
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Password -->
            <div class="relative mb-4">
                <input wire:model="form.password" 
                       id="password"
                       type="password" 
                       placeholder="Password" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring"
                       required>
                <button type="button" 
                        class="absolute inset-y-0 right-3 flex items-center"
                        onclick="togglePassword()">
                    <i class="far fa-eye-slash text-gray-400"></i>
                </button>
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['form.password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> 
                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <div class="mb-4 text-right">
                <a href="<?php echo e(route('password.request')); ?>" class="text-sm text-gray-600 hover:underline">
                    Forgot password?
                </a>
            </div>
            <!-- Remember Me -->
            <label class="flex items-center mb-4">
                <input wire:model="form.remember" 
                       type="checkbox" 
                       class="mr-2 rounded border-gray-300">
                <span class="text-sm">Remember me</span>
                
            </label>

            <!-- Submit Button -->
            <button type="submit" 
                    class="w-full px-4 py-3 font-semibold text-white bg-black rounded-md hover:bg-gray-800">
                Log in
            </button>
        </form>

        <!-- Divider -->
        <div class="text-center text-gray-400">or</div>

        <!-- Google Login -->
        <a href="<?php echo e(route('login.google')); ?>" 
           class="flex items-center justify-center w-full px-4 py-2 space-x-2 border rounded-md hover:bg-gray-50">
            <img src="https://www.google.com/favicon.ico" class="w-5 h-5" alt="Google">
            <span>Continue with Google</span>
        </a>

        <!-- Registration Link -->
        <p class="text-sm text-center">
            Don't have an account?
            <a href="<?php echo e(route('register')); ?>" class="font-semibold text-blue-600 hover:underline">Sign Up</a>
        </p>
    </div>
</main>

<?php $__env->startPush('scripts'); ?>
<script>
    function togglePassword() {
        const input = document.getElementById('password');
        const icon = document.querySelector('.fa-eye-slash');
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.replace('fa-eye-slash', 'fa-eye');
        } else {
            input.type = 'password';
            icon.classList.replace('fa-eye', 'fa-eye-slash');
        }
    }
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\laragon\www\travel-platform\resources\views\livewire/pages/auth/login.blade.php ENDPATH**/ ?>