@startuml Sprint 3 - Simple Class Diagram
!theme plain
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}
skinparam stereotype {
    LBackgroundColor LightBlue
}

title Sprint 3: Favorites, Pre-Booking, My Trips & Support (Simplified)

' Core User
class User {
    -id: int
    -name: string
    -email: string
    --
    +favorites(): HasMany
    +bookings(): HasMany
    +supportMessages(): HasMany
}

' Favorites System
class Favorite {
    -id: int
    -user_id: int
    -favoritable_type: string
    -favoritable_id: int
    --
    +user(): BelongsTo
    +favoritable(): MorphTo
}

' Pre-Booking System
class Booking {
    -id: int
    -user_id: int
    -bookable_type: string
    -bookable_id: int
    -booking_date: date
    -adults: int
    -children: int
    -total_amount: decimal
    -status: enum
    --
    +user(): BelongsTo
    +bookable(): MorphTo
    +isPending(): bool
    +isConfirmed(): bool
    +isCancelled(): bool
}

class BookingModal <<Livewire>> {
    +isOpen: bool
    +selectedDate: string
    +adults: int
    +children: int
    --
    +openModal(): void
    +addToProject(): void
    +goToPayment(): RedirectResponse
}

' My Trips
class MyTrips <<Livewire>> {
    +pendingBookings: Collection
    +confirmedBookings: Collection
    +cancelledBookings: Collection
    --
    +cancelBooking(): void
    +setActiveTab(): void
}

' Support System
class SupportMessage {
    -id: int
    -user_id: int
    -subject: string
    -message: text
    -status: enum
    --
    +user(): BelongsTo
    +isOpen(): bool
    +isResolved(): bool
}

class ContactForm <<Livewire>> {
    +subject: string
    +message: string
    --
    +submit(): void
    +resetForm(): void
}

' Product Models (Simplified)
class Activity {
    -id: int
    -title: string
    --
    +bookings(): MorphMany
    +favorites(): MorphMany
}

class Hotel {
    -id: int
    -name: string
    --
    +bookings(): MorphMany
    +favorites(): MorphMany
}

class Transfer {
    -id: int
    -name: string
    --
    +bookings(): MorphMany
    +favorites(): MorphMany
}

' Simple Relationships
User ||--o{ Favorite : "has many"
User ||--o{ Booking : "has many"
User ||--o{ SupportMessage : "has many"

Favorite }o--|| Activity : "can favorite"
Favorite }o--|| Hotel : "can favorite"
Favorite }o--|| Transfer : "can favorite"

Booking }o--|| Activity : "can book"
Booking }o--|| Hotel : "can book"
Booking }o--|| Transfer : "can book"

@enduml
