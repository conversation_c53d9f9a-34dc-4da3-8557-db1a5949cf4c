<div>
    <div class="min-h-screen bg-gray-50 py-8" x-data="stripePayment()" x-init="setTimeout(() => initStripe(), 100)">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center mb-4">
                    <a href="<?php echo e(route('mytrips')); ?>" class="text-gray-500 hover:text-gray-700 mr-4">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7">
                            </path>
                        </svg>
                    </a>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Complete Payment</h1>
                        <p class="text-gray-600">Secure payment for your booking</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Booking Summary -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Booking Summary</h2>

                    <!-- Activity Info -->
                    <div class="flex items-start space-x-4 mb-6">
                        <!--[if BLOCK]><![endif]--><?php if($booking->bookable->image): ?>
                            <img src="<?php echo e(asset('storage/' . $booking->bookable->image)); ?>"
                                class="w-20 h-20 rounded-lg object-cover" alt="<?php echo e($booking->bookable->name); ?>">
                        <?php else: ?>
                            <div
                                class="w-20 h-20 bg-gradient-to-r from-pink-400 to-purple-500 rounded-lg flex items-center justify-center">
                                <span class="text-white text-2xl">🎯</span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <div class="flex-1">
                            <h3 class="font-semibold text-lg text-gray-900"><?php echo e($booking->bookable->name); ?></h3>
                            <p class="text-gray-600"><?php echo e($booking->bookable->destination?->name ?? 'Activity'); ?></p>
                            <span
                                class="inline-block mt-2 bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
                                Pending Payment
                            </span>
                        </div>
                    </div>

                    <!-- Booking Details -->
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Date & Time:</span>
                            <span class="font-medium"><?php echo e($booking->booking_date->format('M j, Y')); ?> at
                                <?php echo e($booking->booking_time); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Participants:</span>
                            <span class="font-medium">
                                <?php echo e($booking->adults); ?> adult<?php echo e($booking->adults > 1 ? 's' : ''); ?>

                                <?php if($booking->children > 0): ?>
                                    + <?php echo e($booking->children); ?> child<?php echo e($booking->children > 1 ? 'ren' : ''); ?>

                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </span>
                        </div>
                        <div class="border-t pt-4">
                            <div class="flex justify-between text-lg font-semibold">
                                <span>Total Amount:</span>
                                <span class="text-green-600"><?php echo e(formatPrice($booking->total_amount)); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Payment Information</h2>

                    <!-- Stripe Payment Form -->
                    <div class="space-y-6">
                        <!-- Card Element -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Card Details</label>
                            <div id="card-element"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-pink-500 focus-within:border-pink-500">
                                <!-- Stripe Elements will create form elements here -->
                            </div>
                            <div id="card-errors" class="text-red-500 text-xs mt-1" role="alert"></div>
                        </div>

                        <!-- Security Notice -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                    </path>
                                </svg>
                                <div>
                                    <h4 class="text-sm font-medium text-blue-800">Secure Payment</h4>
                                    <p class="text-sm text-blue-700 mt-1">Your payment information is encrypted and
                                        secure.
                                        We use Stripe for payment processing.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex gap-4">
                            <button wire:click="cancelPayment"
                                class="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium">
                                Cancel
                            </button>
                            <button @click="processStripePayment()" :disabled="processing"
                                class="flex-1 py-3 px-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-colors font-medium disabled:opacity-50">
                                <span x-show="!processing">💳 Pay <?php echo e(formatPrice($booking->total_amount)); ?></span>
                                <span x-show="processing">Processing...</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stripe JavaScript -->
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        function stripePayment() {
            return {
                stripe: null,
                elements: null,
                cardElement: null,
                processing: false,

                initStripe() {
                    console.log('Initializing Stripe...');

                    if (!window.Stripe) {
                        console.error('Stripe.js not loaded');
                        return;
                    }

                    this.stripe = Stripe('<?php echo e(config('services.stripe.key')); ?>');
                    this.elements = this.stripe.elements();

                    // Create card element
                    this.cardElement = this.elements.create('card', {
                        style: {
                            base: {
                                fontSize: '16px',
                                color: '#424770',
                                fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                                fontSmoothing: 'antialiased',
                                '::placeholder': {
                                    color: '#aab7c4',
                                },
                            },
                            invalid: {
                                color: '#9e2146',
                            },
                        },
                    });

                    // Mount card element
                    const cardElementContainer = document.getElementById('card-element');
                    if (cardElementContainer) {
                        this.cardElement.mount('#card-element');
                        console.log('Stripe card element mounted');
                    } else {
                        console.error('Card element container not found');
                    }

                    // Handle real-time validation errors from the card Element
                    this.cardElement.on('change', ({
                        error
                    }) => {
                        const displayError = document.getElementById('card-errors');
                        if (displayError) {
                            if (error) {
                                displayError.textContent = error.message;
                            } else {
                                displayError.textContent = '';
                            }
                        }
                    });
                },

                async processStripePayment() {
                    if (this.processing) return;

                    this.processing = true;
                    console.log('Processing Stripe payment...');

                    const clientSecret = window.Livewire.find('<?php echo e($_instance->getId()); ?>').clientSecret;
                    console.log('Client secret:', clientSecret ? 'Available' : 'Missing');

                    if (!clientSecret) {
                        alert('Payment initialization failed. Please try again.');
                        this.processing = false;
                        return;
                    }

                    const {
                        error,
                        paymentIntent
                    } = await this.stripe.confirmCardPayment(clientSecret, {
                        payment_method: {
                            card: this.cardElement,
                            billing_details: {
                                name: 'Customer',
                            }
                        }
                    });

                    if (error) {
                        // Show error to customer
                        const errorElement = document.getElementById('card-errors');
                        errorElement.textContent = error.message;
                        this.processing = false;
                    } else {
                        // Payment succeeded
                        if (paymentIntent.status === 'succeeded') {
                            // Call Livewire method to finalize booking
                            await window.Livewire.find('<?php echo e($_instance->getId()); ?>').call('processPayment');
                            this.processing = false;
                        }
                    }
                }
            }
        }
    </script>
</div>
<?php /**PATH C:\laragon\www\travel-platform\resources\views/livewire/payment-page.blade.php ENDPATH**/ ?>