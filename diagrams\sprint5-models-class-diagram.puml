@startuml Sprint 5 - Models Only Class Diagram
!theme plain
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}
skinparam stereotype {
    CBackgroundColor LightBlue
    MBackgroundColor LightGreen
}

title Sprint 5: Change Currency, Use Chatbot & Admin Dashboard - Models Only

' Currency Management Models
class Currency <<Model>> {
    -id: int
    -code: string
    -name: string
    -symbol: string
    -exchange_rate: decimal
    -is_default: boolean
    -is_active: boolean
    -last_updated: timestamp
    -created_at: timestamp
    -updated_at: timestamp
    --
    +scopeActive(query): Builder
    +scopeDefault(query): Builder
    +updateExchangeRate(rate): void
    +convertAmount(amount, toCurrency): decimal
}

class UserPreference <<Model>> {
    -id: int
    -user_id: int
    -currency_code: string
    -language: string
    -timezone: string
    -notifications_enabled: boolean
    -created_at: timestamp
    -updated_at: timestamp
    --
    +user(): BelongsTo
    +currency(): BelongsTo
}

' Chatbot Models
class ChatSession <<Model>> {
    -id: int
    -session_id: string
    -user_id: int
    -started_at: timestamp
    -ended_at: timestamp
    -context_data: json
    -created_at: timestamp
    -updated_at: timestamp
    --
    +user(): BelongsTo
    +messages(): HasMany
    +recentMessages(): HasMany
    +isActive(): bool
    +updateActivity(): void
    +getContextAttribute(): array
    +setContextAttribute(array): void
}

class ChatMessage <<Model>> {
    -id: int
    -chat_session_id: int
    -sender: enum
    -message: text
    -intent: string
    -entities: json
    -response_data: json
    -confidence: decimal
    -created_at: timestamp
    -updated_at: timestamp
    --
    +chatSession(): BelongsTo
    +scopeRecent(query): Builder
    +scopeBySession(query, sessionId): Builder
    +isUserMessage(): bool
    +isBotResponse(): bool
    +getEntitiesAttribute(): array
    +setEntitiesAttribute(array): void
    +hasHighConfidence(): bool
}

' Core User Model
class User <<Model>> {
    -id: int
    -name: string
    -email: string
    -email_verified_at: timestamp
    -password: string
    -remember_token: string
    -preferred_currency: string
    -chat_session_id: string
    -created_at: timestamp
    -updated_at: timestamp
    --
    +chatSessions(): HasMany
    +chatMessages(): HasMany
    +userPreference(): HasOne
    +bookings(): HasMany
    +favorites(): HasMany
    +supportMessages(): HasMany
    +getPreferredCurrency(): Currency
    +setPreferredCurrency(currency): void
    +hasRole(role): bool
}

' Product Models (for dashboard analytics)
class Hotel <<Model>> {
    -id: int
    -destination_id: int
    -name: string
    -slug: string
    -type: string
    -chaine: string
    -short_description: text
    -description: text
    -image: string
    -rating: int
    -sustainability_score: int
    -address: string
    -city: string
    -country: string
    -phone: string
    -website: string
    -email: string
    -roomcount: int
    -status: enum
    -created_at: timestamp
    -updated_at: timestamp
    --
    +destination(): BelongsTo
    +rooms(): HasMany
    +hotelContracts(): HasMany
    +bookings(): MorphMany
    +favorites(): MorphMany
}

class Activity <<Model>> {
    -id: int
    -destination_id: int
    -title: string
    -description: text
    -highlights: text
    -image: string
    -activity_type: string
    -activity_nature: string
    -difficulty_level: string
    -duration: string
    -min_age: int
    -max_age: int
    -type_of_service: string
    -public_address: string
    -map_address: string
    -latitude: decimal
    -longitude: decimal
    -created_at: timestamp
    -updated_at: timestamp
    --
    +destination(): BelongsTo
    +activityContracts(): BelongsToMany
    +meetingPoints(): BelongsToMany
    +bookings(): MorphMany
    +favorites(): MorphMany
    +getName(): string
    +getDifficulty(): string
}

class Transfer <<Model>> {
    -id: int
    -destination_id: int
    -name: string
    -description: text
    -main_image: string
    -transfer_type: string
    -vehicle_type: string
    -min_capacity: int
    -max_capacity: int
    -suit_cases: int
    -small_bag: int
    -tax: decimal
    -details_tax: string
    -suppliers: string
    -created_at: timestamp
    -updated_at: timestamp
    --
    +destination(): BelongsTo
    +transferContracts(): BelongsToMany
    +bookings(): MorphMany
    +favorites(): MorphMany
}

class Destination <<Model>> {
    -id: int
    -name: string
    -country: string
    -continent: string
    -zone: string
    -image: string
    -created_at: timestamp
    -updated_at: timestamp
    --
    +hotels(): HasMany
    +activities(): HasMany
    +transfers(): HasMany
    +favorites(): MorphMany
    +scopeByContinent(query, continent): Builder
    +scopeByCountry(query, country): Builder
}

class Booking <<Model>> {
    -id: int
    -user_id: int
    -bookable_type: string
    -bookable_id: int
    -booking_date: date
    -booking_time: time
    -adults: int
    -children: int
    -total_amount: decimal
    -status: enum
    -payment_status: enum
    -notes: text
    -created_at: timestamp
    -updated_at: timestamp
    --
    +user(): BelongsTo
    +bookable(): MorphTo
    +isPending(): bool
    +isConfirmed(): bool
    +isCancelled(): bool
    +canBeCancelled(): bool
    +getTotalPrice(): decimal
    +scopeByStatus(query, status): Builder
    +scopeByPaymentStatus(query, status): Builder
}

class SupportMessage <<Model>> {
    -id: int
    -user_id: int
    -subject: string
    -message: text
    -status: enum
    -priority: enum
    -admin_response: text
    -responded_at: timestamp
    -created_at: timestamp
    -updated_at: timestamp
    --
    +user(): BelongsTo
    +isOpen(): bool
    +isClosed(): bool
    +isResolved(): bool
    +markAsResolved(): void
    +scopeByStatus(query, status): Builder
    +scopeByPriority(query, priority): Builder
}

class Event <<Model>> {
    -id: int
    -destination_id: int
    -name: string
    -description: text
    -duration: string
    -start_date: date
    -end_date: date
    -status: enum
    -created_at: timestamp
    -updated_at: timestamp
    --
    +destination(): BelongsTo
    +isActive(): bool
    +isUpcoming(): bool
    +scopeActive(query): Builder
    +scopeByDestination(query, destinationId): Builder
}

' Relationships
User ||--o{ ChatSession : "has many"
User ||--o{ ChatMessage : "has many"
User ||--o{ UserPreference : "has one"
User ||--o{ Booking : "has many"
User ||--o{ SupportMessage : "has many"

ChatSession }o--|| User : "belongs to"
ChatSession ||--o{ ChatMessage : "has many"
ChatMessage }o--|| ChatSession : "belongs to"

UserPreference }o--|| User : "belongs to"
UserPreference }o--|| Currency : "belongs to"

Destination ||--o{ Hotel : "has many"
Destination ||--o{ Activity : "has many"
Destination ||--o{ Transfer : "has many"
Destination ||--o{ Event : "has many"

Hotel }o--|| Destination : "belongs to"
Activity }o--|| Destination : "belongs to"
Transfer }o--|| Destination : "belongs to"
Event }o--|| Destination : "belongs to"

Booking }o--|| User : "belongs to"
Booking }o--|| Hotel : "polymorphic"
Booking }o--|| Activity : "polymorphic"
Booking }o--|| Transfer : "polymorphic"

SupportMessage }o--|| User : "belongs to"

@enduml
