@startuml Chatbot System - Complete Class Diagram
!theme plain
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}
skinparam stereotype {
    CBackgroundColor YellowGreen
    ABackgroundColor Wheat
    LBackgroundColor LightBlue
    SBackgroundColor LightCoral
    MBackgroundColor LightGreen
}

title Travel Platform Chatbot - Complete Class Diagram

package "Livewire Components" {
    class Chatbot <<Livewire>> {
        +message: string
        +chat: array
        +sessionId: string
        +isTyping: bool
        -chatbotService: ChatbotService
        --
        +mount(): void
        +send(): void
        +processDelayedMessage(message): void
        +onDestinationSelected(destinationId): void
        +clearChat(): void
        -createNewSession(): string
        -loadChatHistory(): void
        -getChatbotService(): ChatbotService
        -getContextHistory(): array
    }
}

package "Core Services" {
    class ChatbotService {
        -geminiService: GeminiService
        -intentDetectionService: IntentDetectionService
        -contextManager: ContextManager
        -databaseQueryService: DatabaseQueryService
        -recommendationService: RecommendationService
        --
        +processMessage(message, sessionId, contextHistory): array
        -getOrCreateSession(sessionId): ChatSession
        -saveMessage(session, sender, message, intent, entities, responseData): ChatMessage
    }
    
    class GeminiService {
        -apiKey: string
        -modelName: string = "gemini-1.5-flash"
        --
        +detectIntentAndEntities(message, contextHistory): array
        +generateResponse(userMessage, intentData, databaseResults, contextHistory, recommendations): array
        -buildIntentDetectionPrompt(message, contextHistory): string
        -buildResponsePrompt(userMessage, intentData, databaseResults, contextHistory, recommendations): string
        -callGeminiAPI(prompt, config): string
        -parseIntentResponse(response): array
        -cleanResponse(response): string
        -shouldShowDestinationCards(intentData, databaseResults): bool
        -formatContextHistory(contextHistory): string
        -formatDatabaseResults(databaseResults, intent): string
        -formatRecommendations(recommendations): string
    }
    
    class IntentDetectionService {
        -geminiService: GeminiService
        --
        +detectIntentAndEntities(message, contextHistory): array
        -preprocessMessage(message): string
        -validateAndEnhanceResult(result, message, contextHistory): array
        -enhanceEntitiesWithContext(entities, contextHistory): array
        -validateEntities(entities): array
        -fallbackDetection(message): array
        -extractLocationFromContext(contextHistory): string
        -correctSpelling(message): string
        -normalizeMessage(message): string
    }
    
    class ContextManager {
        +updateContext(session, intentData): void
        +getContextForIntent(session, intent): array
        -mergeContextData(currentContext, intentData): array
        -cleanOldContext(context): array
        -getPersistentEntities(context): array
        -getConversationFlow(context): array
    }
    
    class DatabaseQueryService {
        +queryByIntent(intent, entities): array
        +getRecommendations(intent, entities): array
        -queryHotels(entities): array
        -queryActivities(entities): array
        -queryTransfers(entities): array
        -queryDestinations(entities): array
        -formatHotelsForResponse(hotels): array
        -formatActivitiesForResponse(activities): array
        -formatTransfersForResponse(transfers): array
        -formatDestinationsForResponse(destinations): array
        -getHotelRecommendations(entities): array
        -getActivityRecommendations(entities): array
        -getTransferRecommendations(entities): array
        -getDestinationRecommendations(entities): array
        -parsePriceRange(priceString): array
        -parseCapacity(capacityString): int
    }
    
    class RecommendationService {
        +getRecommendations(intent, entities, user, context): array
        +getPopularityBasedRecommendations(intent, entities): array
        +getUserBehaviorRecommendations(user, intent, entities): array
        +getCrossSellingRecommendations(user, intent, entities): array
        +getLocationBasedRecommendations(intent, entities): array
        +getSimilarBudgetRecommendations(intent, entities): array
        +getContextualRecommendations(intent, entities, context): array
        -formatRecommendations(recommendations, intent): array
        -extractUserPreferences(userBookings): array
        -findCrossSellingItems(booking, intent): array
    }
}

package "Data Models" {
    class ChatSession {
        -id: int
        -session_id: string
        -user_id: int
        -started_at: timestamp
        -ended_at: timestamp
        -context_data: json
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +messages(): HasMany
        +recentMessages(): HasMany
        +isActive(): bool
        +updateActivity(): void
        +getContextAttribute(): array
        +setContextAttribute(array): void
    }
    
    class ChatMessage {
        -id: int
        -chat_session_id: int
        -sender: enum
        -message: text
        -intent: string
        -entities: json
        -response_data: json
        -created_at: timestamp
        -updated_at: timestamp
        --
        +chatSession(): BelongsTo
        +scopeRecent(query): Builder
        +scopeBySession(query, sessionId): Builder
        +isUserMessage(): bool
        +isBotResponse(): bool
        +getEntitiesAttribute(): array
        +setEntitiesAttribute(array): void
        +hasHighConfidence(): bool
    }
    
    class User {
        -id: int
        -name: string
        -email: string
        -email_verified_at: timestamp
        -password: string
        -remember_token: string
        -created_at: timestamp
        -updated_at: timestamp
        --
        +chatSessions(): HasMany
        +bookings(): HasMany
        +favorites(): HasMany
        +supportMessages(): HasMany
        +hasRole(role): bool
    }
}

package "Product Models" {
    class Hotel {
        -id: int
        -destination_id: int
        -name: string
        -slug: string
        -type: string
        -rating: int
        -sustainability_score: int
        -city: string
        -country: string
        -status: enum
        --
        +destination(): BelongsTo
        +rooms(): HasMany
        +hotelContracts(): HasMany
        +bookings(): MorphMany
        +favorites(): MorphMany
    }
    
    class Activity {
        -id: int
        -destination_id: int
        -title: string
        -description: text
        -activity_type: string
        -activity_nature: string
        -difficulty_level: string
        -duration: string
        -min_age: int
        -max_age: int
        --
        +destination(): BelongsTo
        +activityContracts(): BelongsToMany
        +meetingPoints(): BelongsToMany
        +bookings(): MorphMany
        +favorites(): MorphMany
        +getName(): string
    }
    
    class Transfer {
        -id: int
        -destination_id: int
        -name: string
        -description: text
        -transfer_type: string
        -vehicle_type: string
        -min_capacity: int
        -max_capacity: int
        -tax: decimal
        --
        +destination(): BelongsTo
        +transferContracts(): BelongsToMany
        +bookings(): MorphMany
        +favorites(): MorphMany
    }
    
    class Destination {
        -id: int
        -name: string
        -country: string
        -continent: string
        -zone: string
        -image: string
        --
        +hotels(): HasMany
        +activities(): HasMany
        +transfers(): HasMany
        +favorites(): MorphMany
    }
    
    class Booking {
        -id: int
        -user_id: int
        -bookable_type: string
        -bookable_id: int
        -booking_date: date
        -booking_time: time
        -adults: int
        -children: int
        -total_amount: decimal
        -status: enum
        -payment_status: enum
        --
        +user(): BelongsTo
        +bookable(): MorphTo
        +isPending(): bool
        +isConfirmed(): bool
        +isCancelled(): bool
    }
}

package "Configuration & Providers" {
    class ChatbotServiceProvider <<ServiceProvider>> {
        +register(): void
        +boot(): void
    }
    
    class ChatbotConfig <<Config>> {
        +enabled: bool
        +session_timeout: int
        +max_context_messages: int
        +intents: array
        +entities: array
        +responses: array
        +gemini: array
    }
}

package "External APIs" {
    class GeminiAPI <<External>> {
        +generateContent(prompt, config): string
        +detectIntent(message): array
        +extractEntities(message): array
    }
}

package "Testing" {
    class ChatbotTest <<Test>> {
        +test_chatbot_page_loads(): void
        +test_intent_detection_service(): void
        +test_database_query_service(): void
    }
    
    class ChatbotDemoTest <<Test>> {
        +test_complete_conversation_flow(): void
        +test_intent_detection(): void
        +test_database_queries(): void
        +test_recommendation_system(): void
    }
}

' Relationships
Chatbot --> ChatbotService : uses
ChatbotService --> GeminiService : uses
ChatbotService --> IntentDetectionService : uses
ChatbotService --> ContextManager : uses
ChatbotService --> DatabaseQueryService : uses
ChatbotService --> RecommendationService : uses

IntentDetectionService --> GeminiService : uses
GeminiService --> GeminiAPI : calls

ChatbotService --> ChatSession : creates/manages
ChatbotService --> ChatMessage : creates
ChatSession --> User : belongs to
ChatSession --> ChatMessage : has many
ChatMessage --> ChatSession : belongs to

DatabaseQueryService --> Hotel : queries
DatabaseQueryService --> Activity : queries
DatabaseQueryService --> Transfer : queries
DatabaseQueryService --> Destination : queries

RecommendationService --> Booking : analyzes
RecommendationService --> User : analyzes

User --> ChatSession : has many
User --> Booking : has many

Hotel --> Destination : belongs to
Activity --> Destination : belongs to
Transfer --> Destination : belongs to

Booking --> User : belongs to
Booking --> Hotel : polymorphic
Booking --> Activity : polymorphic
Booking --> Transfer : polymorphic

ChatbotServiceProvider --> ChatbotService : registers
ChatbotConfig --> ChatbotService : configures

ChatbotTest --> ChatbotService : tests
ChatbotDemoTest --> IntentDetectionService : tests
ChatbotDemoTest --> DatabaseQueryService : tests

@enduml
