<?php

namespace App\Livewire;

use Livewire\Component;
use Carbon\Carbon;
use Livewire\Attributes\On;
use App\Models\Booking;
use App\Models\Activity;
use App\Services\StripeService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BookingModal extends Component
{
    public $isOpen = false;
    public $step = 1; // 1 = date selection, 2 = person selection, 3 = payment form
    public $activity;
    public $selectedDate = null;
    public $selectedTime = null;
    public $adults = 1;
    public $children = 0;
    public $currentMonth;
    public $currentYear;
    public $availableTimes = ['10:00 AM', '12:00 PM', '14:00 PM', '16:00 PM'];

    // Payment form fields
    public $cardNumber = '';
    public $expiryDate = '';
    public $cvv = '';
    public $cardholderName = '';
    public $billingAddress = '';
    public $city = '';
    public $postalCode = '';
    public $country = '';

    // Stripe fields
    public $paymentIntentId = '';
    public $clientSecret = '';

    public function mount()
    {
        $this->currentMonth = Carbon::now()->month;
        $this->currentYear = Carbon::now()->year;
    }

    #[On('openBookingModal')]
    public function openModal($activityId)
    {
        Log::info('BookingModal openModal called with activityId: ' . $activityId);
        $this->activity = Activity::find($activityId);
        $this->isOpen = true;
        $this->step = 1;
        $this->selectedDate = null;
        $this->selectedTime = null;
        $this->adults = 1;
        $this->children = 0;
    }

    public function closeModal()
    {
        $this->isOpen = false;
        $this->step = 1;
        $this->resetPaymentForm();
    }

    public function resetPaymentForm()
    {
        $this->cardNumber = '';
        $this->expiryDate = '';
        $this->cvv = '';
        $this->cardholderName = '';
        $this->billingAddress = '';
        $this->city = '';
        $this->postalCode = '';
        $this->country = '';
    }

    public function selectDate($date)
    {
        $this->selectedDate = $date;
    }

    public function selectTime($time)
    {
        $this->selectedTime = $time;
    }

    public function nextStep()
    {
        if ($this->step == 1 && $this->selectedDate && $this->selectedTime) {
            $this->step = 2;
        }
    }

    public function previousStep()
    {
        if ($this->step == 2) {
            $this->step = 1;
        } elseif ($this->step == 3) {
            $this->step = 2;
        }
    }

    public function goToPayment()
    {
        if ($this->step == 2) {
            // Check if user is authenticated
            if (!Auth::check()) {
                session()->flash('error', 'Please login to proceed with payment.');
                return;
            }

            // Validate booking details
            if (!$this->selectedDate || !$this->selectedTime || !$this->activity) {
                session()->flash('error', 'Please complete all booking details.');
                return;
            }

            // Calculate total price
            $totalPrice = $this->getTotalPrice();

            // Create the booking with pending payment status first
            $booking = Booking::create([
                'user_id' => Auth::id(),
                'bookable_type' => Activity::class,
                'bookable_id' => $this->activity->id,
                'booking_date' => $this->selectedDate,
                'booking_time' => $this->selectedTime,
                'adults' => $this->adults,
                'children' => $this->children,
                'total_amount' => $totalPrice,
                'status' => 'pending',
                'payment_status' => 'pending'
            ]);

            // Create Stripe service instance
            $stripeService = new StripeService();

            // Create Stripe Payment Intent
            $paymentIntentResult = $stripeService->createPaymentIntent(
                $totalPrice,
                'usd',
                [
                    'booking_id' => $booking->id,
                    'activity_id' => $this->activity->id,
                    'user_id' => Auth::id(),
                    'booking_date' => $this->selectedDate,
                    'booking_time' => $this->selectedTime,
                    'adults' => $this->adults,
                    'children' => $this->children,
                ]
            );

            if ($paymentIntentResult['success']) {
                // Update booking with payment intent ID
                $booking->update([
                    'payment_intent_id' => $paymentIntentResult['payment_intent_id']
                ]);

                $this->paymentIntentId = $paymentIntentResult['payment_intent_id'];
                $this->clientSecret = $paymentIntentResult['client_secret'];
                $this->step = 3;
            } else {
                // Delete the booking if payment intent creation failed
                $booking->delete();
                session()->flash('error', 'Unable to initialize payment. Please try again.');
            }
        }
    }

    public function incrementAdults()
    {
        $this->adults++;
    }

    public function decrementAdults()
    {
        if ($this->adults > 1) {
            $this->adults--;
        }
    }

    public function incrementChildren()
    {
        $this->children++;
    }

    public function decrementChildren()
    {
        if ($this->children > 0) {
            $this->children--;
        }
    }

    public function previousMonth()
    {
        if ($this->currentMonth == 1) {
            $this->currentMonth = 12;
            $this->currentYear--;
        } else {
            $this->currentMonth--;
        }
    }

    public function nextMonth()
    {
        if ($this->currentMonth == 12) {
            $this->currentMonth = 1;
            $this->currentYear++;
        } else {
            $this->currentMonth++;
        }
    }

    public function getCalendarDays()
    {
        $firstDay = Carbon::create($this->currentYear, $this->currentMonth, 1);
        $lastDay = $firstDay->copy()->endOfMonth();
        $startOfWeek = $firstDay->copy()->startOfWeek();
        $endOfWeek = $lastDay->copy()->endOfWeek();

        $days = [];
        $current = $startOfWeek->copy();

        while ($current <= $endOfWeek) {
            $days[] = [
                'date' => $current->copy(),
                'isCurrentMonth' => $current->month == $this->currentMonth,
                'isToday' => $current->isToday(),
                'isPast' => $current->isPast() && !$current->isToday(),
            ];
            $current->addDay();
        }

        return $days;
    }

    public function confirmBooking()
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            session()->flash('error', 'Please login to book activities.');
            return redirect()->route('login');
        }

        // Validate required fields
        if (!$this->selectedDate || !$this->selectedTime || !$this->activity) {
            session()->flash('error', 'Please complete all booking details.');
            return;
        }

        // Calculate total price
        $adultPrice = $this->activity->activityContracts->min('adult_price') ?? 299;
        $childPrice = $this->activity->activityContracts->min('child_price') ?? 199;
        $totalPrice = ($adultPrice * $this->adults) + ($childPrice * $this->children);

        // Create the booking with pending status (no payment yet)
        Booking::create([
            'user_id' => Auth::id(),
            'bookable_type' => Activity::class,
            'bookable_id' => $this->activity->id,
            'booking_date' => $this->selectedDate,
            'booking_time' => $this->selectedTime,
            'adults' => $this->adults,
            'children' => $this->children,
            'total_amount' => $totalPrice,
            'status' => 'pending',
            'payment_status' => 'pending'
        ]);

        // Close the modal
        $this->closeModal();

        // Show success message
        session()->flash('success', 'Activity added to your trips! Complete payment to confirm your booking.');

        // Dispatch event for trip added (not confirmed yet)
        $this->dispatch('trip-added');
    }

    public function processPayment()
    {
        // This method is called after successful Stripe payment
        // It should update an existing booking to confirmed status

        // Check if user is authenticated
        if (!Auth::check()) {
            session()->flash('error', 'Please login to complete payment.');
            return;
        }

        // Validate payment intent
        if (!$this->paymentIntentId) {
            session()->flash('error', 'Payment initialization failed. Please try again.');
            return;
        }

        // Find the booking associated with this payment intent
        $booking = Booking::where('payment_intent_id', $this->paymentIntentId)->first();

        if (!$booking) {
            session()->flash('error', 'Booking not found. Please contact support.');
            return;
        }

        // Update booking status to confirmed after successful payment
        $booking->update([
            'status' => 'confirmed',
            'payment_status' => 'paid'
        ]);

        // Close the modal
        $this->closeModal();

        // Show success message
        session()->flash('success', 'Payment successful! Your booking has been confirmed.');

        // Dispatch event
        $this->dispatch('booking-confirmed');
    }



    public function getTotalPrice()
    {
        if (!$this->activity) {
            return 0;
        }

        $adultPrice = $this->activity->activityContracts->min('adult_price') ?? 299;
        $childPrice = $this->activity->activityContracts->min('child_price') ?? 199;

        return ($adultPrice * $this->adults) + ($childPrice * $this->children);
    }

    public function render()
    {
        return view('livewire.booking-modal', [
            'calendarDays' => $this->getCalendarDays(),
            'monthName' => Carbon::create($this->currentYear, $this->currentMonth, 1)->format('F Y')
        ]);
    }
}