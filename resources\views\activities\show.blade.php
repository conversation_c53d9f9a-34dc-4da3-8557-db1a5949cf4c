@extends('layouts.web')

@section('title', $activity->name)

@section('content')
    <div class="min-h-screen bg-white">
        <!-- Header Section -->
        <div class="relative">
            <!-- Hero Image -->
            <div class="relative h-96 bg-gray-200 overflow-hidden">
                @if ($activity->image)
                    <img src="{{ asset('storage/' . $activity->image) }}" alt="{{ $activity->name }}"
                        class="w-full h-full object-cover">
                @else
                    <div class="w-full h-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center">
                        <span class="text-white text-6xl">🎯</span>
                    </div>
                @endif

                <!-- Action Buttons Overlay -->
                <div class="absolute top-4 right-4 flex space-x-2">
                    <button class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white transition-colors">
                        <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z">
                            </path>
                        </svg>
                    </button>
                    <livewire:favorite-button :model="$activity" />
                </div>
            </div>

            <!-- Activity Header Info -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                    <div class="flex-1">
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $activity->name }}</h1>
                        <p class="text-gray-600 mb-4">From ${{ number_format($bestPrice, 0) }} / person</p>

                        <!-- Tags -->
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                    </path>
                                </svg>
                                4.9 (432)
                            </span>
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                Beach
                            </span>
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                {{ $activity->duration }} hours
                            </span>
                            @if ($activity->difficulty)
                                <span
                                    class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-orange-100 text-orange-800">
                                    {{ ucfirst($activity->difficulty) }}
                                </span>
                            @endif
                        </div>

                        <!-- Quick Info -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Duration</p>
                                    <p class="text-sm text-gray-600">{{ $activity->duration }} hours</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Every day</p>
                                    <p class="text-sm text-gray-600">9:00 AM - 6:00 PM</p>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex-shrink-0">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-2-2V10a2 2 0 012-2h8z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-blue-900">Hosted Languages by our experts</p>
                                    <p class="text-sm text-blue-700">English/Português/Español</p>
                                </div>
                            </div>
                        </div>

                        <!-- Cancellation Policy -->
                        <div class="flex items-center space-x-2 mb-6">
                            <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm text-gray-600">
                                <strong>Cancellation flexibility:</strong> Free cancellation up to 24 hours before the start
                                time
                            </span>
                        </div>
                    </div>

                    <!-- Booking Card -->
                    <div class="lg:ml-8 lg:w-80">
                        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-6 sticky top-4">
                            <div class="flex items-center justify-between mb-4">
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">${{ number_format($bestPrice, 0) }}</p>
                                    <p class="text-sm text-gray-600">per person</p>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center space-x-1">
                                        <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                            </path>
                                        </svg>
                                        <span class="text-sm font-medium">4.9</span>
                                        <span class="text-sm text-gray-600">(432)</span>
                                    </div>
                                    <p class="text-xs text-gray-500">2 guests • Mar 23 • 10:00 PM</p>
                                </div>
                            </div>

                            <button onclick="openBookingModal({{ $activity->id }})"
                                class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-pink-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 mb-3">
                                Add to my project
                            </button>

                            <!-- Debug Test Button -->
                            <button onclick="testModal()"
                                class="w-full bg-red-500 text-white py-2 px-4 rounded-lg text-sm mb-2">
                                🔧 Test Modal (Debug)
                            </button>

                            <p class="text-xs text-center text-gray-500">Activity added to Tempest</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column - Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Host Information -->
                    <div class="bg-white">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ $activity->name }} hosted by
                            <span class="text-pink-600">{{ $activity->destination?->name ?? 'Travel Expert' }}</span>
                        </h2>
                        <div class="flex items-center space-x-4">
                            <div
                                class="w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center">
                                <span
                                    class="text-white font-bold text-lg">{{ substr($activity->destination?->name ?? 'T', 0, 1) }}</span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Expert Guide</p>
                                <p class="text-sm text-gray-600">Superhost • 5 years hosting</p>
                            </div>
                        </div>
                    </div>

                    <!-- What you'll do -->
                    <div class="bg-white">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">What you'll do</h3>
                        <div class="prose prose-gray max-w-none">
                            <p class="text-gray-700 leading-relaxed">
                                {{ $activity->description ?? 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.' }}
                            </p>
                            <button class="text-pink-600 font-medium hover:text-pink-700 transition-colors">Read
                                Less</button>
                        </div>
                    </div>

                    <!-- What you'll see -->
                    <div class="bg-white">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">What you'll see</h3>
                        <div class="prose prose-gray max-w-none">
                            <p class="text-gray-700 leading-relaxed mb-4">
                                Experience breathtaking views and discover hidden gems during this amazing activity. You'll
                                visit iconic locations and learn about local culture and history.
                            </p>
                            <button class="text-pink-600 font-medium hover:text-pink-700 transition-colors">Read
                                Less</button>
                        </div>

                        <!-- Points of Interest -->
                        <div class="mt-6 space-y-4">
                            <div class="flex items-start space-x-3">
                                <div
                                    class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                    <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">
                                        {{ $activity->destination?->name ?? 'Main Location' }}</h4>
                                    <p class="text-sm text-gray-600">
                                        During the tour we will visit the opportunity to visit the main attractions around
                                        Dom Dinis, from the spa, Surf at the point,
                                        before to you will view the Vila Chaves, the Dom Dinis, Chaves on the Vila Chaves,
                                        the Dom Dinis, Chaves Church.
                                    </p>
                                </div>
                            </div>

                            @if ($activity->meetingPoints->count() > 0)
                                <div class="flex items-start space-x-3">
                                    <div
                                        class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">
                                            {{ $activity->meetingPoints->first()->name }}</h4>
                                        <p class="text-sm text-gray-600">{{ $activity->meetingPoints->first()->address }}
                                        </p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Meeting and Pickup -->
                    <div class="bg-white">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Meeting and Pickup</h3>

                        @if ($activity->map_address || $activity->meetingPoints->count() > 0)
                            <div class="mb-4">
                                <div class="flex items-center space-x-2 mb-2">
                                    <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="font-medium text-gray-900">Meeting point</span>
                                </div>
                                <p class="text-gray-700 mb-2">
                                    {{ $activity->map_address ?? ($activity->meetingPoints->first()?->address ?? ($activity->public_address ?? 'Meeting point address')) }}
                                </p>

                                @php
                                    $address =
                                        $activity->map_address ??
                                        ($activity->meetingPoints->first()?->address ??
                                            ($activity->public_address ?? ''));
                                @endphp

                                @if ($address)
                                    <a href="https://www.google.com/maps/search/?api=1&query={{ urlencode($address) }}"
                                        target="_blank"
                                        class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14">
                                            </path>
                                        </svg>
                                        Open in Google Maps
                                    </a>
                                @endif
                            </div>

                            <!-- Interactive Map -->
                            <div class="relative h-64 bg-gray-200 rounded-lg overflow-hidden mb-6">
                                <div id="activity-map" class="w-full h-full rounded-lg"></div>
                            </div>

                            <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    // Initialize the map
                                    const map = L.map('activity-map').setView([38.7223, -9.1393], 13); // Default to Lisbon

                                    // Add OpenStreetMap tiles
                                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                    }).addTo(map);

                                    // Address to geocode
                                    const address = @json($activity->map_address ?? ($activity->meetingPoints->first()?->address ?? ($activity->public_address ?? '')));

                                    if (address) {
                                        // Use Nominatim (OpenStreetMap) geocoding service
                                        fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`)
                                            .then(response => response.json())
                                            .then(data => {
                                                if (data && data.length > 0) {
                                                    const lat = parseFloat(data[0].lat);
                                                    const lon = parseFloat(data[0].lon);

                                                    // Set map view to the geocoded location
                                                    map.setView([lat, lon], 15);

                                                    // Add a marker
                                                    const marker = L.marker([lat, lon]).addTo(map);

                                                    // Add popup with address
                                                    marker.bindPopup(`
                                                        <div class="text-center">
                                                            <strong>{{ $activity->name }}</strong><br>
                                                            <span class="text-sm text-gray-600">${address}</span>
                                                        </div>
                                                    `).openPopup();
                                                } else {
                                                    console.warn('Address not found:', address);
                                                    // Fallback: try to use latitude/longitude if available
                                                    @if ($activity->latitude && $activity->longitude)
                                                        const lat = {{ $activity->latitude }};
                                                        const lon = {{ $activity->longitude }};
                                                        map.setView([lat, lon], 15);
                                                        L.marker([lat, lon]).addTo(map)
                                                            .bindPopup(`
                                                                <div class="text-center">
                                                                    <strong>{{ $activity->name }}</strong><br>
                                                                    <span class="text-sm text-gray-600">Meeting Point</span>
                                                                </div>
                                                            `).openPopup();
                                                    @endif
                                                }
                                            })
                                            .catch(error => {
                                                console.error('Geocoding error:', error);
                                                // Fallback: try to use latitude/longitude if available
                                                @if ($activity->latitude && $activity->longitude)
                                                    const lat = {{ $activity->latitude }};
                                                    const lon = {{ $activity->longitude }};
                                                    map.setView([lat, lon], 15);
                                                    L.marker([lat, lon]).addTo(map)
                                                        .bindPopup(`
                                                            <div class="text-center">
                                                                <strong>{{ $activity->name }}</strong><br>
                                                                <span class="text-sm text-gray-600">Meeting Point</span>
                                                            </div>
                                                        `).openPopup();
                                                @endif
                                            });
                                    } else if ({{ $activity->latitude ?? 'null' }} && {{ $activity->longitude ?? 'null' }}) {
                                        // Use stored coordinates if no address but coordinates are available
                                        const lat = {{ $activity->latitude }};
                                        const lon = {{ $activity->longitude }};
                                        map.setView([lat, lon], 15);
                                        L.marker([lat, lon]).addTo(map)
                                            .bindPopup(`
                                                <div class="text-center">
                                                    <strong>{{ $activity->name }}</strong><br>
                                                    <span class="text-sm text-gray-600">Meeting Point</span>
                                                </div>
                                            `).openPopup();
                                    }
                                });
                            </script>
                        @endif
                    </div>

                    <!-- What's included / not included -->
                    <div class="bg-white">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- What's included -->
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 mb-4">What's included</h3>
                                <ul class="space-y-3">
                                    <li class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-700">Fully narrated guided tour</span>
                                    </li>
                                    <li class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-700">Indoor and outdoor seating options</span>
                                    </li>
                                    <li class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-green-500 flex-shrink-0" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-700">Tables on board</span>
                                    </li>
                                </ul>
                            </div>

                            <!-- What's not included -->
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 mb-4">What's not included</h3>
                                <ul class="space-y-3">
                                    <li class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-red-500 flex-shrink-0" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-700">Drink bar and snack bar on board for purchase</span>
                                    </li>
                                    <li class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-red-500 flex-shrink-0" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-700">Drink bar and snack bar on board for purchase</span>
                                    </li>
                                    <li class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-red-500 flex-shrink-0" fill="currentColor"
                                            viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-gray-700">Tips</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div class="bg-white">
                    <div class="flex items-center space-x-2 mb-6">
                        <h3 class="text-xl font-bold text-gray-900">Reviews</h3>
                        <div class="flex items-center space-x-1">
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                </path>
                            </svg>
                            <span class="font-bold text-lg">4.9</span>
                            <span class="text-gray-600">(432)</span>
                        </div>
                    </div>

                    <!-- Reviews Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Review 1 -->
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">LG</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Lily Geller</p>
                                    <p class="text-sm text-gray-600">1 week ago</p>
                                </div>
                            </div>
                            <p class="text-sm text-gray-700">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut
                                labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                                laboris nisi ut aliquip ex ea commodo consequat.
                            </p>
                        </div>

                        <!-- Review 2 -->
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">LG</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Lily Geller</p>
                                    <p class="text-sm text-gray-600">1 week ago</p>
                                </div>
                            </div>
                            <p class="text-sm text-gray-700">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut
                                labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                                laboris nisi ut aliquip ex ea commodo consequat.
                            </p>
                        </div>

                        <!-- Review 3 -->
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-r from-pink-400 to-red-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">LG</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Lily Geller</p>
                                    <p class="text-sm text-gray-600">1 week ago</p>
                                </div>
                            </div>
                            <p class="text-sm text-gray-700">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut
                                labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco
                                laboris nisi ut aliquip ex ea commodo consequat.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Similar Activities Section -->
                @if ($similarActivities->count() > 0)
                    <div class="bg-white">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Similar activities</h3>

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                            @foreach ($similarActivities as $index => $similar)
                                <div class="bg-white rounded-xl shadow-sm hover:shadow-xl hover:border-orange-200 border border-transparent transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 group animate-fade-in-up cursor-pointer"
                                    style="animation-delay: {{ $index * 0.15 }}s"
                                    onclick="window.location.href='{{ route('activities.show', $similar->id) }}'">
                                    <div class="relative overflow-hidden">
                                        @if ($similar->image)
                                            <img src="{{ asset('storage/' . $similar->image) }}"
                                                class="w-full h-48 object-cover rounded-t-xl transition-transform duration-700 group-hover:scale-110"
                                                alt="{{ $similar->name }}">
                                        @else
                                            <div
                                                class="w-full h-48 bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center rounded-t-xl">
                                                <span class="text-white text-3xl">🎯</span>
                                            </div>
                                        @endif

                                        {{-- Share and Heart Icons --}}
                                        <div class="absolute top-3 left-3 right-3 flex justify-between z-10">
                                            <button
                                                class="bg-white/80 backdrop-blur-sm p-2 rounded-full hover:bg-white hover:scale-110 transform transition-all duration-300 relative z-20"
                                                onclick="event.stopPropagation()">
                                                <svg class="w-4 h-4 text-gray-600 hover:text-orange-500 transition-colors duration-300"
                                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z">
                                                    </path>
                                                </svg>
                                            </button>
                                            <div onclick="event.stopPropagation()">
                                                <livewire:favorite-button :model="$similar" :key="'similar-' . $similar->id" />
                                            </div>
                                        </div>

                                        {{-- Rating Badge --}}
                                        <div
                                            class="absolute top-3 right-16 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium hover:bg-white hover:scale-110 transform transition-all duration-300">
                                            <span class="animate-star-twinkle">⭐ 4.6</span>
                                        </div>

                                        <!-- Hover Overlay -->
                                        <div
                                            class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                        </div>
                                    </div>

                                    <div class="p-4">
                                        <div
                                            class="text-sm text-gray-500 mb-1 group-hover:text-gray-700 transition-colors duration-300">
                                            {{ $similar->destination?->name ?? 'Activity' }}</div>
                                        <h3
                                            class="font-bold text-lg mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors duration-300">
                                            {{ $similar->name }}</h3>
                                        <div class="flex items-center gap-4 text-xs text-gray-500 mb-3">
                                            <span
                                                class="flex items-center gap-1 hover:text-blue-500 transition-colors duration-300">🕒
                                                {{ $similar->duration }}h</span>
                                            <span
                                                class="flex items-center gap-1 hover:text-orange-500 transition-colors duration-300">🎯
                                                {{ $similar->difficulty ?? 'Adventure' }}</span>
                                            <span class="hover:text-purple-500 transition-colors duration-300">+2</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <span
                                                    class="text-xl font-bold group-hover:text-orange-600 transition-colors duration-300">
                                                    ${{ number_format($similar->activityContracts->min('adult_price') ?? 299, 0) }}
                                                </span>
                                                <span class="text-sm text-gray-500">/person</span>
                                            </div>
                                            <button
                                                onclick="event.stopPropagation(); openBookingModal({{ $similar->id }})"
                                                class="bg-black text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 hover:scale-105 transform transition-all duration-300">
                                                Add to project
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Right Column - Sidebar -->
            <div class="lg:col-span-1">
                <!-- Additional booking options or related content can go here -->
            </div>
        </div>
    </div>
    </div>

    <script>
        function openBookingModal(activityId) {
            console.log('Opening booking modal for activity:', activityId);

            // Try multiple methods to ensure the modal opens
            // Method 1: Custom event (for Alpine.js)
            window.dispatchEvent(new CustomEvent('openBookingModal', {
                detail: {
                    activityId: activityId
                }
            }));

            // Method 2: Livewire dispatch (backup)
            if (typeof Livewire !== 'undefined') {
                Livewire.dispatch('openBookingModal', {
                    activityId: activityId
                });
            }
        }

        function testModal() {
            console.log('Test modal function called');
            alert('Test button clicked! Check console for debug info.');

            // Test if Livewire component exists
            if (typeof Livewire !== 'undefined') {
                console.log('Livewire is available');
                console.log('Livewire components:', Livewire.all());
            } else {
                console.log('Livewire is NOT available');
            }

            // Test Alpine.js
            if (typeof Alpine !== 'undefined') {
                console.log('Alpine.js is available');
            } else {
                console.log('Alpine.js is NOT available');
            }

            // Try to trigger the modal
            openBookingModal({{ $activity->id }});
        }

        // Debug logging
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Activity details page loaded');
            console.log('Livewire available:', typeof Livewire !== 'undefined');
            console.log('Alpine available:', typeof Alpine !== 'undefined');
        });
    </script>
@endsection
