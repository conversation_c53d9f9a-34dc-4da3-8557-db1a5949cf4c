@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Transfer Header -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="relative h-64 md:h-80">
                @if($transfer->main_image)
                    <img src="{{ asset('storage/' . $transfer->main_image) }}" 
                         alt="{{ $transfer->name }}" 
                         class="w-full h-full object-cover">
                @else
                    <div class="w-full h-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center">
                        <span class="text-white text-6xl">🚗</span>
                    </div>
                @endif
                <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
                    <div class="p-6 text-white">
                        <h1 class="text-3xl md:text-4xl font-bold mb-2">{{ $transfer->name }}</h1>
                        @if($transfer->destination)
                            <p class="text-lg opacity-90">📍 {{ $transfer->destination->name }}, {{ $transfer->destination->country }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Description -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Transfer</h2>
                    <p class="text-gray-700 leading-relaxed">{{ $transfer->description }}</p>
                </div>

                <!-- Transfer Details -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Transfer Details</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🚐</span>
                            <div>
                                <p class="font-semibold text-gray-900">Vehicle Type</p>
                                <p class="text-gray-600">{{ $transfer->vehicle_type }}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🎯</span>
                            <div>
                                <p class="font-semibold text-gray-900">Transfer Type</p>
                                <p class="text-gray-600">{{ $transfer->transfer_type }}</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">👥</span>
                            <div>
                                <p class="font-semibold text-gray-900">Capacity</p>
                                <p class="text-gray-600">{{ $transfer->min_capacity }} - {{ $transfer->max_capacity }} passengers</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🧳</span>
                            <div>
                                <p class="font-semibold text-gray-900">Luggage</p>
                                <p class="text-gray-600">{{ $transfer->suit_cases ?? 0 }} suitcases, {{ $transfer->small_bag ?? 0 }} small bags</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Booking Card -->
                <div class="bg-white rounded-xl shadow-lg p-6 sticky top-8">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Book This Transfer</h3>
                    
                    @if($transfer->tax)
                        <div class="mb-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Starting from</span>
                                <span class="text-2xl font-bold text-green-600">${{ number_format($transfer->tax, 0) }}</span>
                            </div>
                            <p class="text-sm text-gray-500">{{ $transfer->details_tax ?? 'Per transfer' }}</p>
                        </div>
                    @endif

                    <button class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        Book Now
                    </button>

                    <div class="mt-4 text-center">
                        <p class="text-sm text-gray-500">Free cancellation available</p>
                    </div>
                </div>

                <!-- Destination Info -->
                @if($transfer->destination)
                <div class="bg-white rounded-xl shadow-lg p-6 mt-6">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Destination</h3>
                    <div class="flex items-center space-x-3 mb-3">
                        @if($transfer->destination->image)
                            <img src="{{ asset('storage/' . $transfer->destination->image) }}" 
                                 alt="{{ $transfer->destination->name }}" 
                                 class="w-12 h-12 rounded-lg object-cover">
                        @else
                            <div class="w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 rounded-lg flex items-center justify-center">
                                <span class="text-white text-lg">🌍</span>
                            </div>
                        @endif
                        <div>
                            <p class="font-semibold text-gray-900">{{ $transfer->destination->name }}</p>
                            <p class="text-sm text-gray-600">{{ $transfer->destination->country }}</p>
                        </div>
                    </div>
                    <a href="{{ route('destinations.show', $transfer->destination->id) }}" 
                       class="text-pink-600 hover:text-pink-700 text-sm font-medium">
                        Explore destination →
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
