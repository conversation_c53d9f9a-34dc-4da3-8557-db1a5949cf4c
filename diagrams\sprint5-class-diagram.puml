@startuml Sprint 5 - Currency Change, Chatbot & Admin Dashboard
!theme plain
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}
skinparam stereotype {
    CBackgroundColor YellowGreen
    ABackgroundColor Wheat
    LBackgroundColor LightBlue
    SBackgroundColor LightCoral
}

title Sprint 5: Change Currency, Use Chatbot & Admin Dashboard

package "Currency Management" {
    class Currency {
        -id: int
        -code: string
        -name: string
        -symbol: string
        -exchange_rate: decimal
        -is_default: boolean
        -is_active: boolean
        -last_updated: timestamp
        -created_at: timestamp
        -updated_at: timestamp
        --
        +scopeActive(query): Builder
        +scopeDefault(query): Builder
        +updateExchangeRate(rate): void
        +convertAmount(amount, toCurrency): decimal
    }
    
    class CurrencyService {
        +getAllActiveCurrencies(): Collection
        +getDefaultCurrency(): Currency
        +convertPrice(amount, fromCurrency, toCurrency): decimal
        +updateExchangeRates(): void
        +setUserCurrency(user, currency): void
        +getUserCurrency(user): Currency
    }
    
    class CurrencyController {
        +index(): JsonResponse
        +convert(request): JsonResponse
        +setUserCurrency(request): JsonResponse
        +updateRates(): JsonResponse
    }
    
    class CurrencyConverter <<Livewire>> {
        +selectedCurrency: string
        +availableCurrencies: Collection
        +currentRate: decimal
        --
        +mount(): void
        +changeCurrency(currencyCode): void
        +convertPrice(amount): decimal
        +render(): View
    }
    
    class ExchangeRateAPI {
        +fetchLatestRates(): array
        +getRate(fromCurrency, toCurrency): decimal
        +updateAllRates(): bool
    }
}

package "Chatbot System" {
    class ChatbotController {
        +index(): View
        +sendMessage(request): JsonResponse
        +getHistory(): JsonResponse
        +clearHistory(): JsonResponse
    }
    
    class ChatMessage {
        -id: int
        -user_id: int
        -session_id: string
        -message: text
        -response: text
        -intent: string
        -entities: json
        -confidence: decimal
        -context: json
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +isUserMessage(): bool
        +isBotResponse(): bool
        +getEntities(): array
        +getContext(): array
    }
    
    class ChatbotService {
        +processMessage(message, userId): array
        +detectIntent(message): string
        +extractEntities(message): array
        +generateResponse(intent, entities, context): string
        +updateContext(context, newData): array
        +getRecommendations(intent, entities): Collection
    }
    
    class IntentDetector {
        +detectIntent(message): array
        +classifyMessage(message): string
        +getConfidenceScore(message, intent): decimal
        +trainModel(data): void
    }
    
    class EntityExtractor {
        +extractDestination(message): string
        +extractDateRange(message): array
        +extractBudget(message): decimal
        +extractPersonCount(message): int
        +extractActivityType(message): string
    }
    
    class ContextManager {
        +getContext(sessionId): array
        +updateContext(sessionId, data): void
        +clearContext(sessionId): void
        +hasContext(sessionId, key): bool
    }
    
    class RecommendationEngine {
        +getHotelRecommendations(criteria): Collection
        +getActivityRecommendations(criteria): Collection
        +getDestinationRecommendations(criteria): Collection
        +getPopularProducts(): Collection
        +getCrossSellProducts(product): Collection
        +getBudgetSimilarProducts(product, budget): Collection
    }
    
    class GeminiAPIService {
        +sendRequest(prompt): string
        +generateResponse(context): string
        +analyzeIntent(message): array
        +extractEntities(message): array
    }
    
    class ChatbotWidget <<Livewire>> {
        +messages: Collection
        +currentMessage: string
        +isTyping: bool
        +sessionId: string
        --
        +mount(): void
        +sendMessage(): void
        +showTypingIndicator(): void
        +hideTypingIndicator(): void
        +loadHistory(): void
        +render(): View
    }
}

package "Admin Dashboard" {
    class AdminDashboardController {
        +index(): View
        +getKPIData(): JsonResponse
        +getChartData(type): JsonResponse
        +getRevenueData(): JsonResponse
        +getBookingStats(): JsonResponse
    }
    
    class DashboardWidget <<Filament>> {
        +getStats(): array
        +getChartData(): array
        +getFilters(): array
        +canView(): bool
    }
    
    class ProductCountWidget <<Filament>> {
        +getStats(): array
        +getHotelCount(): int
        +getActivityCount(): int
        +getTransferCount(): int
        +getDestinationCount(): int
    }
    
    class RevenueChartWidget <<Filament>> {
        +getChartData(): array
        +getRevenueByProduct(): array
        +getRevenueByMonth(): array
        +getRevenueByDestination(): array
    }
    
    class BookingStatusWidget <<Filament>> {
        +getChartData(): array
        +getBookingsByStatus(): array
        +getBookingTrends(): array
    }
    
    class ChatbotPerformanceWidget <<Filament>> {
        +getStats(): array
        +getIntentAccuracy(): decimal
        +getResponseTime(): decimal
        +getUserSatisfaction(): decimal
        +getPopularIntents(): array
    }
    
    class CurrencyUsageWidget <<Filament>> {
        +getChartData(): array
        +getCurrencyDistribution(): array
        +getConversionRates(): array
    }
    
    class GeofencingWidget <<Filament>> {
        +getMapData(): array
        +getBookingsByRegion(): array
        +getRevenueByLocation(): array
        +getOccupancyRates(): array
    }
}

package "Analytics & KPI" {
    class AnalyticsService {
        +getProductCounts(): array
        +getRevenueBreakdown(): array
        +getBookingStatistics(): array
        +getChatbotMetrics(): array
        +getCurrencyUsage(): array
        +getGeofencingData(): array
    }
    
    class KPICalculator {
        +calculateOccupancyRate(destination): decimal
        +calculateAverageRevenue(productType): decimal
        +calculateConversionRate(): decimal
        +calculateCustomerSatisfaction(): decimal
        +calculateChatbotEfficiency(): decimal
    }
    
    class ReportGenerator {
        +generateDashboardReport(): array
        +generateRevenueReport(period): array
        +generateBookingReport(period): array
        +generateChatbotReport(period): array
        +exportToPDF(data): Response
        +exportToExcel(data): Response
    }
}

package "User & Session Management" {
    class User {
        -id: int
        -name: string
        -email: string
        -preferred_currency: string
        -chat_session_id: string
        --
        +chatMessages(): HasMany
        +getPreferredCurrency(): Currency
        +setPreferredCurrency(currency): void
    }
    
    class UserPreference {
        -id: int
        -user_id: int
        -currency_code: string
        -language: string
        -timezone: string
        -notifications_enabled: boolean
        --
        +user(): BelongsTo
    }
}

' Relationships
User ||--o{ ChatMessage : "has many"
User ||--o{ UserPreference : "has one"

Currency ||--o{ UserPreference : "preferred currency"

ChatMessage }o--|| User : "belongs to"

' Service Dependencies
CurrencyService ..> Currency : "uses"
CurrencyService ..> ExchangeRateAPI : "uses"

ChatbotService ..> IntentDetector : "uses"
ChatbotService ..> EntityExtractor : "uses"
ChatbotService ..> ContextManager : "uses"
ChatbotService ..> RecommendationEngine : "uses"
ChatbotService ..> GeminiAPIService : "uses"

AnalyticsService ..> KPICalculator : "uses"
AnalyticsService ..> ReportGenerator : "uses"

' Widget Dependencies
ProductCountWidget ..> AnalyticsService : "uses"
RevenueChartWidget ..> AnalyticsService : "uses"
BookingStatusWidget ..> AnalyticsService : "uses"
ChatbotPerformanceWidget ..> AnalyticsService : "uses"
CurrencyUsageWidget ..> AnalyticsService : "uses"
GeofencingWidget ..> AnalyticsService : "uses"

@enduml
