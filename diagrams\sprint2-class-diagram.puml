@startuml Sprint 2 - Manage Destinations and Products
!theme plain
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}
skinparam stereotype {
    CBackgroundColor YellowGreen
    ABackgroundColor Wheat
}

title Sprint 2: Manage Destinations, Hotels, Activities, Transfers, Markets & Contracts

package "Destination Management" {
    class Destination {
        -id: int
        -name: string
        -country: string
        -continent: string
        -zone: string
        -image: string
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotels(): HasMany
        +activities(): HasMany
        +transfers(): HasMany
        +favorites(): MorphMany
        +scopeByContinent(query, continent): Builder
        +scopeByCountry(query, country): Builder
    }
    
    class DestinationController {
        +index(): View
        +show(id): View
        +store(request): RedirectResponse
        +update(request, id): RedirectResponse
        +destroy(id): RedirectResponse
    }
    
    class DestinationResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
}

package "Market Management" {
    class Market {
        -id: int
        -name: string
        -code: string
        -currency: string
        -timezone: string
        -status: enum
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotelContracts(): BelongsToMany
        +activityContracts(): BelongsToMany
        +transferContracts(): BelongsToMany
        +isActive(): bool
    }
    
    class MarketResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
}

package "Hotel Management" {
    class Hotel {
        -id: int
        -destination_id: int
        -name: string
        -slug: string
        -type: string
        -chaine: string
        -short_description: text
        -description: text
        -image: string
        -rating: int
        -sustainability_score: int
        -address: string
        -city: string
        -country: string
        -phone: string
        -website: string
        -email: string
        -roomcount: int
        -status: enum
        -created_at: timestamp
        -updated_at: timestamp
        --
        +destination(): BelongsTo
        +rooms(): HasMany
        +hotelContracts(): HasMany
        +bookings(): MorphMany
        +favorites(): MorphMany
    }
    
    class Room {
        -id: int
        -hotel_id: int
        -name: string
        -type: string
        -capacity: int
        -size: decimal
        -amenities: json
        -description: text
        -images: json
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotel(): BelongsTo
        +allocations(): HasMany
        +releases(): HasMany
    }
    
    class HotelResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
    
    class RoomResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
}

package "Activity Management" {
    class Activity {
        -id: int
        -destination_id: int
        -title: string
        -description: text
        -highlights: text
        -image: string
        -activity_type: string
        -activity_nature: string
        -difficulty_level: string
        -duration: string
        -min_age: int
        -max_age: int
        -type_of_service: string
        -public_address: string
        -map_address: string
        -latitude: decimal
        -longitude: decimal
        -created_at: timestamp
        -updated_at: timestamp
        --
        +destination(): BelongsTo
        +activityContracts(): BelongsToMany
        +meetingPoints(): BelongsToMany
        +bookings(): MorphMany
        +favorites(): MorphMany
        +getName(): string
        +getDifficulty(): string
    }
    
    class MeetingPoint {
        -id: int
        -name: string
        -address: string
        -latitude: decimal
        -longitude: decimal
        -description: text
        -created_at: timestamp
        -updated_at: timestamp
        --
        +activities(): BelongsToMany
    }
    
    class ActivityResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
}

package "Transfer Management" {
    class Transfer {
        -id: int
        -destination_id: int
        -name: string
        -description: text
        -main_image: string
        -transfer_type: string
        -vehicle_type: string
        -min_capacity: int
        -max_capacity: int
        -suit_cases: int
        -small_bag: int
        -tax: decimal
        -details_tax: string
        -suppliers: string
        -created_at: timestamp
        -updated_at: timestamp
        --
        +destination(): BelongsTo
        +transferContracts(): BelongsToMany
        +bookings(): MorphMany
    }
    
    class TransferResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
}

package "Contract Management" {
    class HotelContract {
        -id: int
        -contract_number: string
        -type: string
        -main_supplier: string
        -contract_status: enum
        -signed_date: date
        -start_date: date
        -end_date: date
        -created_at: timestamp
        -updated_at: timestamp
        --
        +hotels(): BelongsToMany
        +markets(): BelongsToMany
        +allocations(): HasMany
        +releases(): HasMany
        +isActive(): bool
    }
    
    class ActivityContract {
        -id: int
        -contract_number: string
        -type: string
        -main_supplier: string
        -contract_status: enum
        -signed_date: date
        -start_date: date
        -end_date: date
        -adult_price: decimal
        -child_price: decimal
        -created_at: timestamp
        -updated_at: timestamp
        --
        +activities(): BelongsToMany
        +markets(): BelongsToMany
        +isActive(): bool
    }
    
    class TransferContract {
        -id: int
        -contract_number: string
        -type: string
        -main_supplier: string
        -contract_status: enum
        -signed_date: date
        -start_date: date
        -end_date: date
        -created_at: timestamp
        -updated_at: timestamp
        --
        +transfers(): BelongsToMany
        +markets(): BelongsToMany
        +isActive(): bool
    }
    
    class HotelContractResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
    
    class ActivityContractResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
    
    class TransferContractResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
    }
}

' Relationships
Destination ||--o{ Hotel : "has many"
Destination ||--o{ Activity : "has many"
Destination ||--o{ Transfer : "has many"

Hotel ||--o{ Room : "has many"
Hotel ||--o{ HotelContract : "belongs to many"

Activity ||--o{ ActivityContract : "belongs to many"
Activity ||--o{ MeetingPoint : "belongs to many"

Transfer ||--o{ TransferContract : "belongs to many"

Market ||--o{ HotelContract : "belongs to many"
Market ||--o{ ActivityContract : "belongs to many"
Market ||--o{ TransferContract : "belongs to many"

@enduml
