<div x-data="{
    formatCardNumber(event) {
            let value = event.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            if (formattedValue.length > 19) formattedValue = formattedValue.substr(0, 19);
            event.target.value = formattedValue;
            $wire.set('cardNumber', formattedValue);
        },
        formatExpiryDate(event) {
            let value = event.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            event.target.value = value;
            $wire.set('expiryDate', value);
        }
}">
    <!-- Debug: Always visible indicator -->
    <div class="fixed bottom-4 right-4 bg-blue-500 text-white p-2 rounded text-xs z-40">
        Modal Component Loaded - isOpen: {{ $isOpen ? 'true' : 'false' }}
    </div>

    @if ($isOpen)
        <!-- Modal Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
            wire:click="closeModal">
            <!-- Modal Content -->
            <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto shadow-2xl" wire:click.stop>
                <!-- Debug Info -->
                <div class="bg-yellow-100 p-2 text-xs">
                    Debug: Modal is open, Activity ID: {{ $activity?->id ?? 'None' }}, Step: {{ $step }}
                </div>

                @if ($step == 1)
                    <!-- Step 1: Date and Time Selection -->
                    <div class="p-6">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h2 class="text-xl font-bold text-gray-900">{{ $activity?->name ?? 'Activity' }}</h2>
                                <p class="text-sm text-gray-500">Select the date and time you want to explore with TRAVEL
                                    SHAPER activities</p>
                            </div>
                            <button wire:click="closeModal" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Calendar -->
                        <div class="mb-6">
                            <!-- Calendar Header -->
                            <div class="flex items-center justify-between mb-4">
                                <button wire:click="previousMonth" class="p-2 hover:bg-gray-100 rounded-full">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                </button>
                                <h3 class="text-lg font-semibold">{{ $monthName }}</h3>
                                <button wire:click="nextMonth" class="p-2 hover:bg-gray-100 rounded-full">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </button>
                            </div>

                            <!-- Calendar Grid -->
                            <div class="grid grid-cols-7 gap-1 mb-2">
                                <div class="text-center text-xs font-medium text-gray-500 py-2">Sun</div>
                                <div class="text-center text-xs font-medium text-gray-500 py-2">Mon</div>
                                <div class="text-center text-xs font-medium text-gray-500 py-2">Tue</div>
                                <div class="text-center text-xs font-medium text-gray-500 py-2">Wed</div>
                                <div class="text-center text-xs font-medium text-gray-500 py-2">Thu</div>
                                <div class="text-center text-xs font-medium text-gray-500 py-2">Fri</div>
                                <div class="text-center text-xs font-medium text-gray-500 py-2">Sat</div>
                            </div>

                            <div class="grid grid-cols-7 gap-1">
                                @foreach ($calendarDays as $day)
                                    <button wire:click="selectDate('{{ $day['date']->format('Y-m-d') }}')"
                                        class="h-10 text-sm rounded-lg transition-all duration-200
                                            @if ($day['isPast']) text-gray-300 cursor-not-allowed
                                            @elseif (!$day['isCurrentMonth'])
                                                text-gray-400 hover:bg-gray-100
                                            @elseif ($selectedDate == $day['date']->format('Y-m-d'))
                                                bg-black text-white
                                            @elseif ($day['isToday'])
                                                bg-pink-100 text-pink-600 hover:bg-pink-200
                                            @else
                                                hover:bg-gray-100 @endif"
                                        @if ($day['isPast']) disabled @endif>
                                        {{ $day['date']->day }}
                                    </button>
                                @endforeach
                            </div>
                        </div>

                        @if ($selectedDate)
                            <div class="text-center text-sm text-gray-600 mb-4">
                                Clear date
                            </div>
                        @endif

                        <!-- Available Time Slots -->
                        @if ($selectedDate)
                            <div class="mb-6">
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Available Time Slot</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    @foreach ($availableTimes as $time)
                                        <button wire:click="selectTime('{{ $time }}')"
                                            class="p-3 text-sm border rounded-lg transition-all duration-200
                                                @if ($selectedTime == $time) border-black bg-black text-white
                                                @else
                                                    border-gray-200 hover:border-gray-300 hover:bg-gray-50 @endif">
                                            {{ $time }}
                                        </button>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Action Buttons -->
                        <div class="flex gap-3">
                            <button wire:click="closeModal"
                                class="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                Cancel
                            </button>
                            <button wire:click="nextStep"
                                class="flex-1 py-3 px-4 rounded-lg text-white transition-colors
                                    @if ($selectedDate && $selectedTime) bg-black hover:bg-gray-800
                                    @else
                                        bg-gray-300 cursor-not-allowed @endif"
                                @if (!$selectedDate || !$selectedTime) disabled @endif>
                                Next
                            </button>
                        </div>
                    </div>
                @elseif ($step == 2)
                    <!-- Step 2: Person Selection -->
                    <div class="p-6">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h2 class="text-xl font-bold text-gray-900">{{ $activity?->name ?? 'Activity' }}</h2>
                                <p class="text-sm text-gray-500">How many people will be joining?</p>
                            </div>
                            <button wire:click="closeModal" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Selected Date & Time Display -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-6">
                            <div class="flex items-center gap-2 text-sm text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span>{{ \Carbon\Carbon::parse($selectedDate)->format('F j, Y') }} at
                                    {{ $selectedTime }}</span>
                            </div>
                        </div>

                        <!-- Person Selection -->
                        <div class="space-y-4 mb-6">
                            <!-- Adults -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">Adults</h4>
                                    <p class="text-sm text-gray-500">Age 13+</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <button wire:click="decrementAdults"
                                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50
                                            @if ($adults <= 1) opacity-50 cursor-not-allowed @endif"
                                        @if ($adults <= 1) disabled @endif>
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M20 12H4"></path>
                                        </svg>
                                    </button>
                                    <span class="w-8 text-center font-medium">{{ $adults }}</span>
                                    <button wire:click="incrementAdults"
                                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Children -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">Children</h4>
                                    <p class="text-sm text-gray-500">Age 2-12</p>
                                </div>
                                <div class="flex items-center gap-3">
                                    <button wire:click="decrementChildren"
                                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50
                                            @if ($children <= 0) opacity-50 cursor-not-allowed @endif"
                                        @if ($children <= 0) disabled @endif>
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M20 12H4"></path>
                                        </svg>
                                    </button>
                                    <span class="w-8 text-center font-medium">{{ $children }}</span>
                                    <button wire:click="incrementChildren"
                                        class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Total Price Display -->
                        @if ($activity)
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Total</span>
                                    <span class="text-xl font-bold text-gray-900">
                                        {{ formatPrice(($activity->activityContracts->min('adult_price') ?? 299) * $adults + ($activity->activityContracts->min('child_price') ?? 199) * $children) }}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">
                                    {{ $adults }} adult{{ $adults > 1 ? 's' : '' }}
                                    @if ($children > 0)
                                        + {{ $children }} child{{ $children > 1 ? 'ren' : '' }}
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Action Buttons -->
                        <div class="flex gap-3">
                            <button wire:click="previousStep"
                                class="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                Back
                            </button>
                            <button wire:click="confirmBooking"
                                class="flex-1 py-3 px-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-lg hover:from-pink-600 hover:to-purple-700 transition-colors">
                                Add to Project
                            </button>
                            <button wire:click="goToPayment"
                                class="flex-1 py-3 px-4 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                                Book Now
                            </button>
                        </div>
                    </div>
                @elseif ($step == 3)
                    <!-- Step 3: Payment Form -->
                    <div class="p-6">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h2 class="text-xl font-bold text-gray-900">Payment Details</h2>
                                <p class="text-sm text-gray-500">Complete your booking with secure payment</p>
                            </div>
                            <button wire:click="closeModal" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <!-- Booking Summary -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-6">
                            <h3 class="font-medium text-gray-900 mb-2">Booking Summary</h3>
                            <div class="space-y-1 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Activity:</span>
                                    <span class="font-medium">{{ $activity?->name ?? 'Activity' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Date:</span>
                                    <span class="font-medium">{{ $selectedDate }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Time:</span>
                                    <span class="font-medium">{{ $selectedTime }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Guests:</span>
                                    <span class="font-medium">{{ $adults }}
                                        adult{{ $adults > 1 ? 's' : '' }}{{ $children > 0 ? ', ' . $children . ' child' . ($children > 1 ? 'ren' : '') : '' }}</span>
                                </div>
                                <div class="border-t pt-2 mt-2">
                                    <div class="flex justify-between font-bold">
                                        <span>Total:</span>
                                        <span>${{ $this->getTotalPrice() }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Stripe Payment Form -->
                        <div id="stripe-payment-form" class="space-y-4" x-data="stripePayment()"
                            x-init="setTimeout(() => initStripe(), 100)">
                            <!-- Card Information -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Card Information</h4>

                                <!-- Stripe Card Element -->
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Card Details</label>
                                    <div id="card-element"
                                        class="w-full px-3 py-3 border border-gray-300 rounded-lg focus-within:ring-2 focus-within:ring-pink-500 focus-within:border-pink-500">
                                        <!-- Stripe Elements will create form elements here -->
                                    </div>
                                    <div id="card-errors" class="text-red-500 text-xs mt-1" role="alert"></div>
                                </div>
                            </div>

                            <!-- Billing Address -->
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Billing Address</h4>

                                <!-- Address -->
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                                    <input type="text" wire:model="billingAddress" placeholder="123 Main Street"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                    @error('billingAddress')
                                        <span class="text-red-500 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <!-- City -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                        <input type="text" wire:model="city" placeholder="New York"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                        @error('city')
                                            <span class="text-red-500 text-xs">{{ $message }}</span>
                                        @enderror
                                    </div>

                                    <!-- Postal Code -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                                        <input type="text" wire:model="postalCode" placeholder="10001"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                        @error('postalCode')
                                            <span class="text-red-500 text-xs">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Country -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                                    <select wire:model="country"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500">
                                        <option value="">Select Country</option>
                                        <option value="US">United States</option>
                                        <option value="CA">Canada</option>
                                        <option value="UK">United Kingdom</option>
                                        <option value="FR">France</option>
                                        <option value="DE">Germany</option>
                                        <option value="ES">Spain</option>
                                        <option value="IT">Italy</option>
                                        <option value="AU">Australia</option>
                                    </select>
                                    @error('country')
                                        <span class="text-red-500 text-xs">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-3">
                                <button type="button" wire:click="previousStep"
                                    class="flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                    Back
                                </button>
                                <button type="button" @click="processStripePayment()" id="submit-payment"
                                    class="flex-1 py-3 px-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-colors font-medium disabled:opacity-50"
                                    :disabled="processing">
                                    <span x-show="!processing">💳 Pay ${{ $this->getTotalPrice() }}</span>
                                    <span x-show="processing">Processing...</span>
                                </button>
                            </div>
                        </div>

                        <!-- Security Notice -->
                        <div class="mt-4 text-center">
                            <p class="text-xs text-gray-500">
                                🔒 Your payment information is secure and encrypted
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>

<script>
    // Event listener for opening the booking modal
    document.addEventListener('DOMContentLoaded', function() {
        console.log('BookingModal script loaded');

        window.addEventListener('openBookingModal', function(event) {
            console.log('BookingModal received custom event:', event.detail);

            if (typeof Livewire !== 'undefined') {
                console.log('Dispatching to Livewire...');
                Livewire.dispatch('openBookingModal', {
                    activityId: event.detail.activityId
                });
            } else {
                console.error('Livewire not available');
            }
        });
    });
</script>

<!-- Stripe JavaScript -->
<script src="https://js.stripe.com/v3/"></script>
<script>
    function stripePayment() {
        return {
            stripe: null,
            elements: null,
            cardElement: null,
            processing: false,

            initStripe() {
                console.log('Initializing Stripe...');
                this.stripe = Stripe('{{ config('services.stripe.key') }}');
                this.elements = this.stripe.elements();

                // Create card element
                this.cardElement = this.elements.create('card', {
                    style: {
                        base: {
                            fontSize: '16px',
                            color: '#424770',
                            '::placeholder': {
                                color: '#aab7c4',
                            },
                        },
                        invalid: {
                            color: '#9e2146',
                        },
                    },
                });

                // Mount card element
                this.cardElement.mount('#card-element');
                console.log('Stripe card element mounted');

                // Handle real-time validation errors from the card Element
                this.cardElement.on('change', ({
                    error
                }) => {
                    const displayError = document.getElementById('card-errors');
                    if (error) {
                        displayError.textContent = error.message;
                    } else {
                        displayError.textContent = '';
                    }
                });
            },

            async processStripePayment() {
                if (this.processing) return;

                this.processing = true;
                console.log('Processing Stripe payment...');

                const clientSecret = @this.clientSecret;
                console.log('Client secret:', clientSecret ? 'Available' : 'Missing');

                if (!clientSecret) {
                    alert('Payment initialization failed. Please try again.');
                    this.processing = false;
                    return;
                }

                const {
                    error,
                    paymentIntent
                } = await this.stripe.confirmCardPayment(clientSecret, {
                    payment_method: {
                        card: this.cardElement,
                        billing_details: {
                            name: @this.cardholderName || 'Customer',
                            address: {
                                line1: @this.billingAddress,
                                city: @this.city,
                                postal_code: @this.postalCode,
                                country: @this.country,
                            }
                        }
                    }
                });

                if (error) {
                    // Show error to customer
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = error.message;
                    this.processing = false;
                } else {
                    // Payment succeeded
                    if (paymentIntent.status === 'succeeded') {
                        // Call Livewire method to finalize booking
                        await @this.call('processPayment');
                        this.processing = false;
                    }
                }
            }
        }
    }
</script>
