@startuml Sprint 3 - Favorites, Pre-Booking, My Trips & Support
!theme plain
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}
skinparam stereotype {
    CBackgroundColor YellowGreen
    ABackgroundColor Wheat
    LBackgroundColor LightBlue
}

title Sprint 3: Manage Favorites, Pre-Book Products, View My Trips & Contact Support

package "User Management" {
    class User {
        -id: int
        -name: string
        -email: string
        -email_verified_at: timestamp
        -password: string
        -remember_token: string
        -created_at: timestamp
        -updated_at: timestamp
        --
        +favorites(): HasMany
        +bookings(): HasMany
        +supportMessages(): HasMany
        +hasRole(role): bool
    }
}

package "Favorites Management" {
    class Favorite {
        -id: int
        -user_id: int
        -favoritable_type: string
        -favoritable_id: int
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +favoritable(): MorphTo
        +scopeByType(query, type): Builder
    }
    
    class FavoriteController {
        +index(): View
        +store(request): JsonResponse
        +destroy(id): JsonResponse
        +toggle(request): JsonResponse
    }
    
    class FavoriteService {
        +addToFavorites(user, model): Favorite
        +removeFromFavorites(user, model): bool
        +toggleFavorite(user, model): array
        +getUserFavorites(user, type): Collection
        +isFavorited(user, model): bool
    }
}

package "Pre-Booking System" {
    class Booking {
        -id: int
        -user_id: int
        -bookable_type: string
        -bookable_id: int
        -booking_date: date
        -booking_time: time
        -adults: int
        -children: int
        -total_amount: decimal
        -status: enum
        -payment_status: enum
        -notes: text
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +bookable(): MorphTo
        +isPending(): bool
        +isConfirmed(): bool
        +isCancelled(): bool
        +canBeCancelled(): bool
        +getTotalPrice(): decimal
        +scopeByStatus(query, status): Builder
        +scopeByPaymentStatus(query, status): Builder
    }
    
    class BookingModal <<Livewire>> {
        +activity: Activity
        +isOpen: bool
        +step: int
        +selectedDate: string
        +selectedTime: string
        +adults: int
        +children: int
        --
        +openModal(activityId): void
        +closeModal(): void
        +nextStep(): void
        +previousStep(): void
        +addToProject(): void
        +goToPayment(): RedirectResponse
        +calculateTotal(): decimal
    }
    
    class BookingService {
        +createPreBooking(data): Booking
        +updateBooking(booking, data): Booking
        +cancelBooking(booking): bool
        +confirmBooking(booking): bool
        +calculatePrice(bookable, adults, children): decimal
    }
}

package "My Trips Management" {
    class MyTrips <<Livewire>> {
        +confirmedBookings: Collection
        +pendingBookings: Collection
        +cancelledBookings: Collection
        +activeTab: string
        --
        +mount(): void
        +setActiveTab(tab): void
        +cancelBooking(bookingId): void
        +getBookingsByStatus(status): Collection
        +render(): View
    }
    
    class MyTripsController {
        +index(): View
        +show(id): View
        +cancel(id): RedirectResponse
        +downloadInvoice(id): Response
    }
}

package "Support System" {
    class SupportMessage {
        -id: int
        -user_id: int
        -subject: string
        -message: text
        -status: enum
        -priority: enum
        -admin_response: text
        -responded_at: timestamp
        -created_at: timestamp
        -updated_at: timestamp
        --
        +user(): BelongsTo
        +isOpen(): bool
        +isClosed(): bool
        +isResolved(): bool
        +markAsResolved(): void
        +scopeByStatus(query, status): Builder
        +scopeByPriority(query, priority): Builder
    }
    
    class SupportController {
        +index(): View
        +create(): View
        +store(request): RedirectResponse
        +show(id): View
        +update(request, id): RedirectResponse
    }
    
    class SupportMessageResource <<Filament>> {
        +form(form): Form
        +table(table): Table
        +getRelations(): array
        +getPages(): array
        +canCreate(): bool
        +canEdit(record): bool
    }
    
    class ContactForm <<Livewire>> {
        +subject: string
        +message: string
        +priority: string
        --
        +submit(): void
        +resetForm(): void
        +rules(): array
        +render(): View
    }
}

package "Product Models" {
    class Hotel {
        -id: int
        -name: string
        -destination_id: int
        --
        +bookings(): MorphMany
        +favorites(): MorphMany
        +destination(): BelongsTo
    }
    
    class Activity {
        -id: int
        -title: string
        -destination_id: int
        --
        +bookings(): MorphMany
        +favorites(): MorphMany
        +destination(): BelongsTo
        +getName(): string
    }
    
    class Transfer {
        -id: int
        -name: string
        -destination_id: int
        --
        +bookings(): MorphMany
        +favorites(): MorphMany
        +destination(): BelongsTo
    }
    
    class Destination {
        -id: int
        -name: string
        -country: string
        --
        +favorites(): MorphMany
        +hotels(): HasMany
        +activities(): HasMany
        +transfers(): HasMany
    }
}

package "Notification System" {
    class NotificationService {
        +sendBookingConfirmation(booking): void
        +sendCancellationNotification(booking): void
        +sendSupportResponse(message): void
        +sendFavoriteDestinationUpdate(user, destination): void
    }

    class BookingConfirmationMail <<Mailable>> {
        +booking: Booking
        --
        +build(): Mailable
    }

    class SupportResponseMail <<Mailable>> {
        +message: SupportMessage
        --
        +build(): Mailable
    }
}

package "Event System" {
    class BookingCreated <<Event>> {
        +booking: Booking
        --
        +__construct(booking): void
    }

    class BookingCancelled <<Event>> {
        +booking: Booking
        --
        +__construct(booking): void
    }

    class SupportMessageCreated <<Event>> {
        +message: SupportMessage
        --
        +__construct(message): void
    }
}

' Relationships
User ||--o{ Favorite : "has many"
User ||--o{ Booking : "has many"
User ||--o{ SupportMessage : "has many"

Favorite }o--|| User : "belongs to"
Favorite }o--|| Hotel : "favoritable"
Favorite }o--|| Activity : "favoritable"
Favorite }o--|| Transfer : "favoritable"
Favorite }o--|| Destination : "favoritable"

Booking }o--|| User : "belongs to"
Booking }o--|| Hotel : "bookable"
Booking }o--|| Activity : "bookable"
Booking }o--|| Transfer : "bookable"

SupportMessage }o--|| User : "belongs to"

Hotel ||--o{ Booking : "morphMany"
Activity ||--o{ Booking : "morphMany"
Transfer ||--o{ Booking : "morphMany"

Hotel ||--o{ Favorite : "morphMany"
Activity ||--o{ Favorite : "morphMany"
Transfer ||--o{ Favorite : "morphMany"
Destination ||--o{ Favorite : "morphMany"

@enduml
