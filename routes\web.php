<?php

use App\Http\Controllers\ActivityController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\GoogleLoginController;
use App\Http\Controllers\EditProfileController;
use App\Http\Controllers\ProfileController;
use App\Livewire\Chatbot;
use App\Livewire\emp;
use App\Livewire\EmptyTrips;
use App\Livewire\MyTrips;
use App\Livewire\Favorites;
use App\Livewire\ListProduct;
use Filament\Pages\Auth\Login;
use Illuminate\Support\Facades\Route;

Route::get('/', ListProduct::class)->name('home'); // Updated this line
Route::get('/mytrips', MyTrips::class)->middleware('auth')->name('mytrips'); // Updated this line
Route::get('/chatbot', Chatbot::class)->middleware('auth')->name('chatbot'); // Updated this line
Route::get('/destinations', [\App\Http\Controllers\DestinationController::class, 'index'])->name('destinations'); // All destinations page
Route::get('/destinations/{id}', [\App\Http\Controllers\DestinationController::class, 'show'])->name('destinations.show'); // Single destination page

// Transfer routes
Route::get('/transfers/{id}', [\App\Http\Controllers\TransferController::class, 'show'])->name('transfers.show'); // Single transfer page

Route::get('/favorites', Favorites::class)->middleware('auth')->name('favorites'); // Favorites page
Route::get('/profile', [EditProfileController::class, 'edit'])->middleware('auth')->name('profile.edit');

// Support routes
Route::get('/support', [\App\Http\Controllers\SupportController::class, 'index'])->name('support.contact');
Route::post('/support', [\App\Http\Controllers\SupportController::class, 'store'])->name('support.store');

// Activity detail route
Route::get('/activities/{id}', [\App\Http\Controllers\ActivityController::class, 'show'])->name('activities.show');

// Hotel detail route
Route::get('/hotels/{id}', [\App\Http\Controllers\HotelController::class, 'show'])->name('hotels.show');

// Hotel seasonal pricing route
Route::post('/hotels/seasonal-pricing', [\App\Http\Controllers\HotelController::class, 'getSeasonalPricing'])->name('hotels.seasonal-pricing');

// Currency switching route
Route::post('/currency/switch', [\App\Http\Controllers\CurrencyController::class, 'switch'])->name('currency.switch');
Route::patch('/profile', [EditProfileController::class, 'update'])->middleware('auth')->name('profile.update');
//Route::patch('/profile', [EditProfileController::class, 'update'])->middleware('auth')->name('profile.update');
Route::delete('/profile', [EditProfileController::class, 'destroy'])->middleware('auth')->name('profile.destroy');
Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

// Add this with your other auth routes
Route::middleware('auth')->group(function () {
    // ... your other routes ...

    Route::post('/logout', [EditProfileController::class, 'logout'])
        ->name('logout');

    // Favorites routes
    Route::post('/favorites/toggle', [\App\Http\Controllers\FavoriteController::class, 'toggle'])
        ->name('favorites.toggle');

    // Payment page for pending bookings
    Route::get('/payment/{bookingId}', \App\Livewire\PaymentPage::class)->name('payment.page');
});

    Route::get('/login/google', [GoogleLoginController::class, 'redirect'])
    ->name('login.google');

Route::get('/login/google/callback', [GoogleLoginController::class, 'callback']);

// Stripe Webhook (no auth middleware needed)
Route::post('/stripe/webhook', [\App\Http\Controllers\StripeWebhookController::class, 'handleWebhook'])
    ->name('stripe.webhook');

// Include test routes (remove in production)
require __DIR__.'/test-stripe.php';

require __DIR__.'/auth.php';