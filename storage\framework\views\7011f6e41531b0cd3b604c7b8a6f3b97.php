<?php

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

?>

<main class="flex items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-md px-6 py-10 space-y-6">
        <div class="flex flex-col items-center space-y-3">
            <img src="<?php echo e(asset('storage/avatar.jpg')); ?>" alt="Avatar" class="w-20 h-20 rounded-full">
            <h2 class="text-2xl font-bold text-center">Let's get started</h2>
            <p class="text-center text-gray-500">Create your account and find beautiful destinations</p>
        </div>

        <form wire:submit="register">
            <?php echo csrf_field(); ?>

            <!-- Name -->
            <div class="mb-4">
                <input wire:model="name" 
                       type="text" 
                       placeholder="Full Name" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring" 
                       required autofocus>
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Email -->
            <div class="mb-4">
                <input wire:model="email" 
                       type="email" 
                       placeholder="Email" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring" 
                       required>
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Password -->
            <div class="relative mb-4">
                <input wire:model="password" 
                       id="password"
                       type="password" 
                       placeholder="Password" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring" 
                       required>
                <button type="button" 
                        class="absolute inset-y-0 right-3 flex items-center"
                        onclick="togglePassword()">
                    <i class="far fa-eye-slash text-gray-400"></i>
                </button>
                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <!-- Confirm Password -->
            <div class="mb-4">
                <input wire:model="password_confirmation" 
                       type="password" 
                       placeholder="Confirm Password" 
                       class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring" 
                       required>
            </div>

            <!-- Terms Checkbox -->
            <label class="flex items-center space-x-2 text-sm mb-4">
                <input wire:model="terms" 
                       type="checkbox" 
                       class="w-4 h-4 border-gray-300 rounded">
                <span>I agree to the <a href="#" class="text-blue-600">Terms</a> and <a href="#" class="text-blue-600">Privacy Policy</a></span>
            </label>
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Submit Button -->
            <button type="submit" 
                    class="w-full px-4 py-3 font-semibold text-white bg-black rounded-md hover:bg-gray-800">
                Sign up
            </button>
        </form>

        <div class="text-center text-gray-400">or</div>

        <!-- Google Login -->
        <a href="<?php echo e(route('login.google')); ?>" 
           class="flex items-center justify-center w-full px-4 py-2 space-x-2 border rounded-md hover:bg-gray-50">
            <img src="https://www.google.com/favicon.ico" class="w-5 h-5" alt="Google">
            <span>Continue with Google</span>
        </a>

        <p class="text-sm text-center">
            Already have an account?
            <a href="<?php echo e(route('login')); ?>" class="font-semibold text-blue-600 hover:underline">Login</a>
        </p>
    </div>
</main>

<?php $__env->startPush('scripts'); ?>
<script>
    function togglePassword() {
        const input = document.getElementById('password');
        const icon = document.querySelector('.fa-eye-slash');
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.replace('fa-eye-slash', 'fa-eye');
        } else {
            input.type = 'password';
            icon.classList.replace('fa-eye', 'fa-eye-slash');
        }
    }
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\laragon\www\travel-platform\resources\views\livewire/pages/auth/register.blade.php ENDPATH**/ ?>