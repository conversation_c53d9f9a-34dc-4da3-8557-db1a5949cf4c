<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Stripe Payment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    @livewireStyles
</head>

<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-4">Test Stripe Payment</h1>

            <div class="mb-4">
                <h2 class="text-lg font-semibold">Sample Activity</h2>
                <p class="text-gray-600">Desert Safari Adventure</p>
                <p class="text-2xl font-bold text-green-600">$299.00</p>
            </div>

            <button onclick="openBookingModal(1)"
                class="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 px-6 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-colors font-medium">
                🎫 Book Now - Test Payment
            </button>

            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                <h3 class="font-semibold text-blue-800">Test Card Numbers:</h3>
                <ul class="text-sm text-blue-700 mt-2">
                    <li><strong>Success:</strong> 4242 4242 4242 4242</li>
                    <li><strong>Decline:</strong> 4000 0000 0000 0002</li>
                    <li><strong>3D Secure:</strong> 4000 0025 0000 3155</li>
                </ul>
                <p class="text-xs text-blue-600 mt-2">
                    Use any future expiry date, any 3-digit CVC, and any postal code.
                </p>
            </div>
        </div>
    </div>

    <!-- Include the booking modal -->
    @livewire('booking-modal')

    @livewireScripts

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <script>
        function openBookingModal(activityId) {
            console.log('Opening booking modal for activity:', activityId);
            // Try both methods
            window.dispatchEvent(new CustomEvent('openBookingModal', {
                detail: {
                    activityId: activityId
                }
            }));
            Livewire.dispatch('openBookingModal', {
                activityId: activityId
            });
        }

        // Debug Alpine.js
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized');
        });

        // Debug the custom event
        window.addEventListener('openBookingModal', (event) => {
            console.log('Custom event received:', event.detail);
        });

        // Listen for booking confirmation
        document.addEventListener('livewire:init', () => {
            console.log('Livewire initialized');
            Livewire.on('booking-confirmed', () => {
                alert('🎉 Booking confirmed successfully! Check your My Trips page.');
            });
        });
    </script>
</body>

</html>
